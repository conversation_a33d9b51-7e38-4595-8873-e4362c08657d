import { execa } from 'execa';
import { logger } from './logger.js';

function compareVersions(version1, version2) {
  const v1Parts = version1.split('.').map(Number);
  const v2Parts = version2.split('.').map(Number);

  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const v1Part = v1Parts[i] || 0;
    const v2Part = v2Parts[i] || 0;

    if (v1Part < v2Part) return -1;
    if (v1Part > v2Part) return 1;
  }

  return 0;
}

export async function checkSystemRequirements() {
  const tasks = [
    {
      name: 'Node.js',
      command: 'node --version',
      minVersion: '18.0.0',
      getVersion: (output) => output.replace('v', '')
    },
    {
      name: 'npm',
      command: 'npm --version',
      minVersion: '7.0.0'
    }
  ];

  for (const task of tasks) {
    try {
      const { stdout } = await execa(task.command, { shell: true });
      const version = task.getVersion ? task.getVersion(stdout) : stdout;
      
      if (compareVersions(version, task.minVersion) < 0) {
        logger.error(
          `${task.name} version ${version} is not supported. Requires ${task.minVersion}+`
        );
        process.exit(1);
      }
    } catch (error) {
      logger.error(`Failed to check ${task.name} version: ${error.message}`);
      process.exit(1);
    }
  }
}

export function validateComponentName(name) {
  if (!/^[a-z0-9-]+$/.test(name)) {
    throw new Error(
      'Component name must be lowercase with hyphens (e.g., "my-component")'
    );
  }
}