import { execa } from 'execa';
import semver from 'semver';
import { logger } from './logger.js';

export async function checkSystemRequirements() {
  const tasks = [
    {
      name: 'Node.js',
      command: 'node --version',
      minVersion: '18.0.0',
      getVersion: (output) => output.replace('v', '')
    },
    {
      name: 'npm',
      command: 'npm --version',
      minVersion: '7.0.0'
    }
  ];

  for (const task of tasks) {
    try {
      const { stdout } = await execa(task.command, { shell: true });
      const version = task.getVersion ? task.getVersion(stdout) : stdout;
      
      if (semver.lt(version, task.minVersion)) {
        logger.error(
          `${task.name} version ${version} is not supported. Requires ${task.minVersion}+`
        );
        process.exit(1);
      }
    } catch (error) {
      logger.error(`Failed to check ${task.name} version: ${error.message}`);
      process.exit(1);
    }
  }
}

export function validateComponentName(name) {
  if (!/^[a-z0-9-]+$/.test(name)) {
    throw new Error(
      'Component name must be lowercase with hyphens (e.g., "my-component")'
    );
  }
}