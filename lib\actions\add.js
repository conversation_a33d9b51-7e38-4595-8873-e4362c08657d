import axios from 'axios';
import { Listr } from 'listr2';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';
import fs from 'fs-extra';
import { registryItemSchema } from '../schemas.js';
import { getCache, setCache } from '../utils/cache.js';
import { getConfig } from '../utils/config.js';
import { logger } from '../utils/logger.js';
import { installDependencies } from '../utils/installer.js';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export async function addAction(componentName, options) {
  const config = await getConfig();
  const tasks = new Listr([
    {
      title: `Adding ${componentName}`,
      task: async (ctx, task) => {
        const subtasks = new Listr([], { concurrent: false, exitOnError: false });
        
        // Check cache first
        subtasks.add({
          title: 'Checking cache',
          task: async () => {
            const cached = await getCache(componentName);
            if (cached && !options.force) {
              throw new Error(`${componentName} already exists (use --force to reinstall)`);
            }
          }
        });

        // Fetch component data
        subtasks.add({
          title: 'Fetching component',
          task: async () => {
            const { data } = await axios.get(`${config.baseUrl}/${componentName}.json`);
            const validated = registryItemSchema.safeParse(data);
            if (!validated.success) {
              throw new Error('Invalid component format');
            }
            ctx.component = validated.data;
          }
        });

        // Process dependencies
        if (!options.deps && ctx.component.registryDependencies?.length) {
          subtasks.add({
            title: 'Processing dependencies',
            task: async () => {
              const depTasks = new Listr([], { concurrent: true });
              
              for (const dep of ctx.component.registryDependencies) {
                const depName = path.basename(dep, '.json');
                depTasks.add({
                  title: depName,
                  task: async () => {
                    await addAction(depName, { ...options, deps: false });
                  }
                });
              }
              
              return depTasks;
            }
          });
        }

        // Install files
        subtasks.add({
          title: 'Installing files',
          task: async () => {
            if (options.dryRun) {
              logger.info(`Would install ${componentName} to ${config.componentsDir}`);
              return;
            }

            for (const file of ctx.component.files) {
              const filePath = path.join(process.cwd(), file.target);
              await fs.mkdir(path.dirname(filePath), { recursive: true });
              await fs.writeFile(filePath, file.content);
            }
          }
        });

        // Update cache
        subtasks.add({
          title: 'Updating cache',
          task: async () => {
            if (!options.dryRun) {
              await setCache(componentName, ctx.component, config.cacheTTL);
            }
          }
        });

        // Install npm dependencies
        if (ctx.component.dependencies?.length && !options.dryRun) {
          subtasks.add({
            title: 'Installing dependencies',
            task: async () => {
              await installDependencies(ctx.component.dependencies);
            }
          });
        }

        return subtasks;
      }
    }
  ], { 
    rendererOptions: { 
      collapse: false,
      collapseErrors: false 
    } 
  });

  try {
    await tasks.run();
    logger.success(`Successfully added ${chalk.bold(componentName)}`);
  } catch (error) {
    logger.error(`Failed to add ${componentName}: ${error.message}`);
    process.exit(1);
  }
}