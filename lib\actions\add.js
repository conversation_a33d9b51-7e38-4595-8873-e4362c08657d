import axios from 'axios';
import { Listr } from 'listr2';
import path from 'path';
import chalk from 'chalk';
import fs from 'fs-extra';
import { getCache, setCache } from '../utils/cache.js';
import { getConfig } from '../utils/config.js';
import { logger } from '../utils/logger.js';
import { installDependencies } from '../utils/installer.js';

export async function addAction(componentName, options = {}) {
  const config = await getConfig();

  // Validate component name
  if (!componentName || typeof componentName !== 'string') {
    logger.error('Component name is required');
    process.exit(1);
  }

  const tasks = new Listr([
    {
      title: `Adding ${chalk.blue(componentName)}`,
      task: async (ctx, task) => {
        const subtasks = new Listr([], { concurrent: false, exitOnError: false });

        // Check cache first (only if not forcing)
        if (!options.force) {
          subtasks.add({
            title: 'Checking cache',
            task: async () => {
              const cached = await getCache(componentName);
              if (cached) {
                throw new Error(`${componentName} already exists (use --force to reinstall)`);
              }
            }
          });
        }

        // Fetch component data from MVPBlocks registry
        subtasks.add({
          title: 'Fetching component from MVPBlocks registry',
          task: async () => {
            try {
              const componentUrl = `${config.baseUrl}/${componentName}.json`;

              const { data } = await axios.get(componentUrl, {
                timeout: 15000,
                headers: {
                  'User-Agent': 'mvpblocks-cli/1.2.0',
                  'Accept': 'application/json'
                }
              });

              // Validate the component data
              if (!data.name || !data.type || !data.files) {
                throw new Error('Invalid component format: missing required fields');
              }

              ctx.component = data;
            } catch (error) {
              if (error.response?.status === 404) {
                throw new Error(`Component "${componentName}" not found in MVPBlocks registry. Visit https://blocks.mvp-subha.me to see available components.`);
              } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
                throw new Error('Network error: Unable to connect to MVPBlocks registry');
              }
              throw new Error(`Failed to fetch component: ${error.message}`);
            }
          }
        });

        // Process registry dependencies (only if not explicitly disabled)
        if (options.deps !== false && ctx.component?.registryDependencies?.length) {
          subtasks.add({
            title: `Processing ${ctx.component.registryDependencies.length} registry dependencies`,
            task: async () => {
              const depTasks = new Listr([], { concurrent: false });

              for (const dep of ctx.component.registryDependencies) {
                // Extract component name from URL (e.g., https://blocks.mvp-subha.me/r/border-beam.json -> border-beam)
                const depName = path.basename(dep, '.json');
                depTasks.add({
                  title: `Installing registry dependency: ${chalk.blue(depName)}`,
                  task: async () => {
                    await addAction(depName, { ...options, deps: false });
                  }
                });
              }

              return depTasks;
            }
          });
        }

        // Install component files
        subtasks.add({
          title: 'Installing component files',
          task: async () => {
            if (options.dryRun) {
              logger.info(`Would install ${componentName} files to project`);
              ctx.component.files?.forEach(file => {
                const targetPath = file.target || `components/ui/${componentName}.tsx`;
                logger.info(`  → ${targetPath}`);
              });
              return;
            }

            if (!ctx.component?.files?.length) {
              throw new Error('No files found in component');
            }

            for (const file of ctx.component.files) {
              // Determine target path - use file.target if available, otherwise generate one
              let targetPath;
              if (file.target) {
                targetPath = file.target;
              } else {
                // Generate target path based on component type
                const extension = ctx.component.type === 'registry:hook' ? '.ts' : '.tsx';
                if (ctx.component.type === 'registry:ui') {
                  targetPath = `components/ui/${componentName}${extension}`;
                } else if (ctx.component.type === 'registry:block') {
                  targetPath = `components/blocks/${componentName}${extension}`;
                } else if (ctx.component.type === 'registry:hook') {
                  targetPath = `hooks/${componentName}${extension}`;
                } else {
                  targetPath = `lib/${componentName}${extension}`;
                }
              }

              const fullPath = path.join(process.cwd(), targetPath);
              await fs.mkdir(path.dirname(fullPath), { recursive: true });
              await fs.writeFile(fullPath, file.content);

              logger.success(`  ✓ Created ${chalk.dim(targetPath)}`);
            }
          }
        });

        // Update cache
        subtasks.add({
          title: 'Updating cache',
          task: async () => {
            if (!options.dryRun) {
              await setCache(componentName, ctx.component, config.cacheTTL);
            }
          }
        });

        // Install npm dependencies
        if (ctx.component?.dependencies?.length) {
          subtasks.add({
            title: `Installing ${ctx.component.dependencies.length} npm dependencies`,
            task: async () => {
              if (options.dryRun) {
                logger.info(`Would install npm dependencies: ${ctx.component.dependencies.join(', ')}`);
                return;
              }
              await installDependencies(ctx.component.dependencies);
            }
          });
        }

        return subtasks;
      }
    }
  ], { 
    rendererOptions: { 
      collapse: false,
      collapseErrors: false 
    } 
  });

  try {
    const ctx = await tasks.run();

    // Show success message with component details
    console.log('\n' + chalk.green('✓ Component installed successfully!'));
    console.log(chalk.blue(`  Component: ${chalk.bold(componentName)}`));
    if (ctx.component?.type) {
      console.log(chalk.blue(`  Type: ${ctx.component.type.replace('registry:', '')}`));
    }
    if (ctx.component?.files?.length) {
      console.log(chalk.blue(`  Files: ${ctx.component.files.length} file(s) created`));
    }
    if (ctx.component?.dependencies?.length) {
      console.log(chalk.blue(`  Dependencies: ${ctx.component.dependencies.length} npm package(s)`));
    }

    console.log('\n' + chalk.dim('You can now import and use the component in your project!'));

  } catch (error) {
    console.log('\n' + chalk.red('✗ Failed to install component'));
    logger.error(error.message);

    if (error.message.includes('not found')) {
      console.log(chalk.dim('\nTip: Visit https://blocks.mvp-subha.me to browse available components'));
    }

    process.exit(1);
  }
}