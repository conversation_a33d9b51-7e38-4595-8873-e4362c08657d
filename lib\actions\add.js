import axios from 'axios';
import { Listr } from 'listr2';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';
import fs from 'fs-extra';
import { registryItemSchema } from '../schemas.js';
import { getCache, setCache } from '../utils/cache.js';
import { getConfig } from '../utils/config.js';
import { logger } from '../utils/logger.js';
import { installDependencies } from '../utils/installer.js';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export async function addAction(componentName, options = {}) {
  const config = await getConfig();

  // Validate component name
  if (!componentName || typeof componentName !== 'string') {
    logger.error('Component name is required');
    process.exit(1);
  }

  const tasks = new Listr([
    {
      title: `Adding ${chalk.blue(componentName)}`,
      task: async (ctx) => {
        const subtasks = new Listr([], { concurrent: false, exitOnError: false });

        // Check cache first (only if not forcing)
        if (!options.force) {
          subtasks.add({
            title: 'Checking cache',
            task: async () => {
              const cached = await getCache(componentName);
              if (cached) {
                throw new Error(`${componentName} already exists (use --force to reinstall)`);
              }
            }
          });
        }

        // Fetch component data from predefined URL pattern
        subtasks.add({
          title: 'Fetching component from remote',
          task: async () => {
            try {
              const componentUrl = `${config.baseUrl}/${componentName}.json`;
              logger.info(`Fetching from: ${componentUrl}`);

              const { data } = await axios.get(componentUrl, {
                timeout: 10000,
                headers: {
                  'User-Agent': 'mvpblocks-cli/1.2.0'
                }
              });

              const validated = registryItemSchema.safeParse(data);
              if (!validated.success) {
                throw new Error(`Invalid component format: ${validated.error.message}`);
              }
              ctx.component = validated.data;
              logger.success(`Successfully fetched ${componentName}`);
            } catch (error) {
              if (error.response?.status === 404) {
                throw new Error(`Component "${componentName}" not found. Check the name and try again.`);
              } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
                throw new Error('Network error: Unable to connect to component registry');
              }
              throw new Error(`Failed to fetch component: ${error.message}`);
            }
          }
        });

        // Process dependencies (only if not explicitly disabled)
        if (options.deps !== false && ctx.component.registryDependencies?.length) {
          subtasks.add({
            title: `Processing ${ctx.component.registryDependencies.length} dependencies`,
            task: async () => {
              const depTasks = new Listr([], { concurrent: false });

              for (const dep of ctx.component.registryDependencies) {
                const depName = path.basename(dep, '.json');
                depTasks.add({
                  title: `Installing dependency: ${depName}`,
                  task: async () => {
                    await addAction(depName, { ...options, deps: false });
                  }
                });
              }

              return depTasks;
            }
          });
        }

        // Install files
        subtasks.add({
          title: 'Installing files',
          task: async () => {
            if (options.dryRun) {
              logger.info(`Would install ${componentName} to ${config.componentsDir}`);
              return;
            }

            for (const file of ctx.component.files) {
              const filePath = path.join(process.cwd(), file.target);
              await fs.mkdir(path.dirname(filePath), { recursive: true });
              await fs.writeFile(filePath, file.content);
            }
          }
        });

        // Update cache
        subtasks.add({
          title: 'Updating cache',
          task: async () => {
            if (!options.dryRun) {
              await setCache(componentName, ctx.component, config.cacheTTL);
            }
          }
        });

        // Install npm dependencies
        if (ctx.component.dependencies?.length && !options.dryRun) {
          subtasks.add({
            title: 'Installing dependencies',
            task: async () => {
              await installDependencies(ctx.component.dependencies);
            }
          });
        }

        return subtasks;
      }
    }
  ], { 
    rendererOptions: { 
      collapse: false,
      collapseErrors: false 
    } 
  });

  try {
    await tasks.run();
    logger.success(`Successfully added ${chalk.bold(componentName)}`);
  } catch (error) {
    logger.error(`Failed to add ${componentName}: ${error.message}`);
    process.exit(1);
  }
}