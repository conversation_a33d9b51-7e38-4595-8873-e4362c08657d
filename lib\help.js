import chalk from 'chalk';
import boxen from 'boxen';

export function showHelp(options = {}) {
  if (options.ui) {
    showUIHelp();
  } else if (options.lib) {
    showLibHelp();
  } else if (options.block) {
    showBlockHelp();
  } else if (options.hook) {
    showHookHelp();
  } else {
    showGeneralHelp();
  }
}

function showGeneralHelp() {
  console.log(boxen(
    chalk.bold.blue('🚀 MVPBlocks CLI Help') + '\n\n' +
    chalk.white('The ultimate component management tool built on top of shadcn/ui\n\n') +
    chalk.yellow('📋 USAGE:\n') +
    chalk.white('  npx mvpblocks@latest <command> [options]\n\n') +
    chalk.yellow('🎯 COMMANDS:\n') +
    chalk.white('  add <component>  Add a component from MVPBlocks registry\n') +
    chalk.white('  list [--type]    List available components\n') +
    chalk.white('  help [--type]    Show help information\n\n') +
    chalk.yellow('✨ EXAMPLES:\n') +
    chalk.white('  npx mvpblocks@latest add about-us-1\n') +
    chalk.white('  npx mvpblocks@latest list --ui\n') +
    chalk.white('  npx mvpblocks@latest help --block\n\n') +
    chalk.cyan('🌐 Registry: ') + chalk.dim('https://blocks.mvp-subha.me/r/\n') +
    chalk.cyan('📖 Browse: ') + chalk.dim('https://blocks.mvp-subha.me'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'blue'
    }
  ));
}

function showUIHelp() {
  console.log(boxen(
    chalk.bold.green('🎨 UI Components Help') + '\n\n' +
    chalk.white('Reusable interface elements and effects\n\n') +
    chalk.yellow('📋 EXAMPLES:\n') +
    chalk.white('  npx mvpblocks@latest add border-beam\n') +
    chalk.white('  npx mvpblocks@latest add pulse-card\n') +
    chalk.white('  npx mvpblocks@latest add spotlight\n') +
    chalk.white('  npx mvpblocks@latest add gradient-text\n\n') +
    chalk.yellow('🎯 COMMON UI COMPONENTS:\n') +
    chalk.white('  • Animated effects (border-beam, spotlight)\n') +
    chalk.white('  • Interactive cards (pulse-card, hover-card)\n') +
    chalk.white('  • Text effects (gradient-text, typewriter)\n') +
    chalk.white('  • Navigation (floating-navbar, breadcrumb)\n') +
    chalk.white('  • Buttons and forms (animated-button, input-otp)\n\n') +
    chalk.dim('💡 Browse all UI components: npx mvpblocks@latest list --ui'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'green'
    }
  ));
}

function showBlockHelp() {
  console.log(boxen(
    chalk.bold.magenta('🧱 Block Components Help') + '\n\n' +
    chalk.white('Complete page sections ready to use\n\n') +
    chalk.yellow('📋 EXAMPLES:\n') +
    chalk.white('  npx mvpblocks@latest add about-us-1\n') +
    chalk.white('  npx mvpblocks@latest add hero-1\n') +
    chalk.white('  npx mvpblocks@latest add pricing-1\n') +
    chalk.white('  npx mvpblocks@latest add contact-1\n\n') +
    chalk.yellow('🎯 COMMON BLOCK COMPONENTS:\n') +
    chalk.white('  • Hero sections (hero-1, hero-2, hero-3)\n') +
    chalk.white('  • About sections (about-us-1, about-us-2)\n') +
    chalk.white('  • Pricing tables (pricing-1, pricing-2)\n') +
    chalk.white('  • Contact forms (contact-1, contact-2)\n') +
    chalk.white('  • Testimonials (testimonials-1, reviews-1)\n') +
    chalk.white('  • Feature showcases (features-1, features-2)\n\n') +
    chalk.dim('💡 Browse all blocks: npx mvpblocks@latest list --block'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'magenta'
    }
  ));
}

function showHookHelp() {
  console.log(boxen(
    chalk.bold.yellow('🪝 Hook Components Help') + '\n\n' +
    chalk.white('Custom React hooks for reusable logic\n\n') +
    chalk.yellow('📋 EXAMPLES:\n') +
    chalk.white('  npx mvpblocks@latest add use-local-storage\n') +
    chalk.white('  npx mvpblocks@latest add use-debounce\n') +
    chalk.white('  npx mvpblocks@latest add use-intersection\n\n') +
    chalk.yellow('🎯 COMMON HOOK COMPONENTS:\n') +
    chalk.white('  • Storage hooks (use-local-storage, use-session)\n') +
    chalk.white('  • Performance hooks (use-debounce, use-throttle)\n') +
    chalk.white('  • UI hooks (use-intersection, use-scroll)\n') +
    chalk.white('  • API hooks (use-fetch, use-mutation)\n') +
    chalk.white('  • Form hooks (use-form-validation, use-input)\n\n') +
    chalk.dim('💡 Browse all hooks: npx mvpblocks@latest list --hook'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'yellow'
    }
  ));
}

function showLibHelp() {
  console.log(boxen(
    chalk.bold.cyan('📚 Library Components Help') + '\n\n' +
    chalk.white('Utility functions and helper libraries\n\n') +
    chalk.yellow('📋 EXAMPLES:\n') +
    chalk.white('  npx mvpblocks@latest add api-client\n') +
    chalk.white('  npx mvpblocks@latest add form-validator\n') +
    chalk.white('  npx mvpblocks@latest add date-utils\n\n') +
    chalk.yellow('🎯 COMMON LIBRARY COMPONENTS:\n') +
    chalk.white('  • API utilities (api-client, http-client)\n') +
    chalk.white('  • Validation (form-validator, schema-validator)\n') +
    chalk.white('  • Date/time (date-utils, time-formatter)\n') +
    chalk.white('  • String utilities (text-utils, slug-generator)\n') +
    chalk.white('  • Math utilities (number-formatter, calculator)\n\n') +
    chalk.dim('💡 Browse all libraries: npx mvpblocks@latest list --lib'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'cyan'
    }
  ));
}
