import { execa } from 'execa';
import ora from 'ora';
import chalk from 'chalk';

const REGISTRY_BASE = 'https://blocks.mvp-subha.me/r';

export async function addComponent(componentName) {
  const spinner = ora(`Adding ${chalk.blue(componentName)} from MVPBlocks registry...`).start();
  
  try {
    const componentUrl = `${REGISTRY_BASE}/${componentName}.json`;
    
    // Use shadcn to add the component
    await execa('npx', ['shadcn@latest', 'add', componentUrl], {
      stdio: 'inherit',
    });
    
    spinner.succeed(`✅ Successfully added ${chalk.green(componentName)}!`);
    
    console.log(chalk.dim('\n💡 Component installed and ready to use in your project!'));
    console.log(chalk.dim(`📍 Registry: ${componentUrl}`));
    
  } catch (error) {
    spinner.fail(`❌ Failed to add ${chalk.red(componentName)}`);
    
    if (error.code === 'ENOENT') {
      console.log(chalk.yellow('\n⚠️  shadcn CLI not found. Please install it first:'));
      console.log(chalk.white('   npm install -g shadcn@latest'));
      console.log(chalk.white('   or use: npx shadcn@latest init'));
    } else if (error.exitCode === 1) {
      console.log(chalk.yellow(`\n⚠️  Component "${componentName}" might not exist in the registry.`));
      console.log(chalk.dim('💡 Use "npx mvpblocks@latest list" to see available components'));
      console.log(chalk.dim('🌐 Or visit: https://blocks.mvp-subha.me'));
    } else {
      console.error(chalk.red('\n❌ Error details:'), error.message);
    }
    
    process.exit(1);
  }
}
