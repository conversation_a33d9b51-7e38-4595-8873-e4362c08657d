// lib/setup.js
import { promises as fs } from 'fs';
import path from 'path';

export async function setupPathAliases() {
  const tsconfigPath = path.join(process.cwd(), 'tsconfig.json');
  
  try {
    const tsconfig = JSON.parse(await fs.readFile(tsconfigPath, 'utf-8'));
    
    if (!tsconfig.compilerOptions.paths) {
      tsconfig.compilerOptions.paths = {};
    }

    // Add required path aliases
    tsconfig.compilerOptions.paths['@/components/*'] = ['./components/*'];
    tsconfig.compilerOptions.paths['@/components/ui/*'] = ['./components/ui/*'];
    tsconfig.compilerOptions.paths['@/components/mvpblocks/*'] = ['./components/mvpblocks/*'];
    tsconfig.compilerOptions.paths['@/lib/*'] = ['./lib/*'];
    tsconfig.compilerOptions.paths['@/hooks/*'] = ['./hooks/*'];

    await fs.writeFile(tsconfigPath, JSON.stringify(tsconfig, null, 2));
    console.log('✅ Updated tsconfig.json with required path aliases');
  } catch (error) {
    console.error('Failed to update tsconfig.json:', error.message);
  }
}