import chalk from 'chalk';
import boxen from 'boxen';
import { table } from 'table';

export async function helpAction(options) {
  if (options.ui) {
    showUIHelp();
  } else if (options.lib) {
    showLibHelp();
  } else if (options.block) {
    showBlockHelp();
  } else if (options.hook) {
    showHookHelp();
  } else {
    showGeneralHelp();
  }
}

function showGeneralHelp() {
  console.log(boxen(
    chalk.bold.blue('MVPBlocks CLI Help') + '\n\n' +
    chalk.white('The ultimate component management tool for fetching and managing UI components\n\n') +
    chalk.yellow('USAGE:\n') +
    chalk.white('  npx mvpblocks@latest <command> [options]\n\n') +
    chalk.yellow('COMMANDS:\n') +
    chalk.white('  help     Display help information\n') +
    chalk.white('  add      Add a component to your project\n') +
    chalk.white('  list     List available components\n\n') +
    chalk.yellow('EXAMPLES:\n') +
    chalk.white('  npx mvpblocks@latest add about-us-1\n') +
    chalk.white('  npx mvpblocks@latest list --ui\n') +
    chalk.white('  npx mvpblocks@latest help --ui\n\n') +
    chalk.dim('For command-specific help, use: npx mvpblocks@latest <command> --help'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'blue'
    }
  ));

  console.log('\n' + chalk.bold('DETAILED COMMAND REFERENCE:'));
  
  const commandData = [
    [chalk.bold('Command'), chalk.bold('Description'), chalk.bold('Key Options')],
    [
      chalk.blue('add <component>'),
      'Fetch and install a component',
      '--force, --dry-run, --no-deps'
    ],
    [
      chalk.blue('list'),
      'Show available components',
      '--ui, --lib, --block, --hook, --json'
    ],
    [
      chalk.blue('help'),
      'Display help information',
      '--ui, --lib, --block, --hook'
    ]
  ];

  console.log(table(commandData, {
    border: {
      topBody: '─',
      topJoin: '┬',
      topLeft: '┌',
      topRight: '┐',
      bottomBody: '─',
      bottomJoin: '┴',
      bottomLeft: '└',
      bottomRight: '┘',
      bodyLeft: '│',
      bodyRight: '│',
      bodyJoin: '│',
      joinBody: '─',
      joinLeft: '├',
      joinRight: '┤',
      joinJoin: '┼'
    }
  }));
}

function showUIHelp() {
  console.log(boxen(
    chalk.bold.green('UI Components Help') + '\n\n' +
    chalk.white('UI components are reusable interface elements\n\n') +
    chalk.yellow('EXAMPLES:\n') +
    chalk.white('  npx mvpblocks@latest add button-primary\n') +
    chalk.white('  npx mvpblocks@latest add modal-dialog\n') +
    chalk.white('  npx mvpblocks@latest list --ui\n\n') +
    chalk.yellow('COMMON UI COMPONENTS:\n') +
    chalk.white('  • Buttons (primary, secondary, outline)\n') +
    chalk.white('  • Forms (input, textarea, select)\n') +
    chalk.white('  • Navigation (navbar, sidebar, breadcrumb)\n') +
    chalk.white('  • Feedback (alert, toast, modal)\n') +
    chalk.white('  • Layout (grid, card, container)'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'green'
    }
  ));
}

function showLibHelp() {
  console.log(boxen(
    chalk.bold.cyan('Library Components Help') + '\n\n' +
    chalk.white('Library components are utility functions and helpers\n\n') +
    chalk.yellow('EXAMPLES:\n') +
    chalk.white('  npx mvpblocks@latest add utils-validation\n') +
    chalk.white('  npx mvpblocks@latest add api-client\n') +
    chalk.white('  npx mvpblocks@latest list --lib\n\n') +
    chalk.yellow('COMMON LIBRARY COMPONENTS:\n') +
    chalk.white('  • Utilities (validation, formatting, helpers)\n') +
    chalk.white('  • API clients (REST, GraphQL)\n') +
    chalk.white('  • State management (stores, reducers)\n') +
    chalk.white('  • Authentication (auth helpers, guards)\n') +
    chalk.white('  • Data processing (parsers, transformers)'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'cyan'
    }
  ));
}

function showBlockHelp() {
  console.log(boxen(
    chalk.bold.magenta('Block Components Help') + '\n\n' +
    chalk.white('Block components are complete page sections\n\n') +
    chalk.yellow('EXAMPLES:\n') +
    chalk.white('  npx mvpblocks@latest add about-us-1\n') +
    chalk.white('  npx mvpblocks@latest add hero-section\n') +
    chalk.white('  npx mvpblocks@latest list --block\n\n') +
    chalk.yellow('COMMON BLOCK COMPONENTS:\n') +
    chalk.white('  • Hero sections (landing page headers)\n') +
    chalk.white('  • About sections (company info, team)\n') +
    chalk.white('  • Feature sections (product highlights)\n') +
    chalk.white('  • Contact sections (forms, info)\n') +
    chalk.white('  • Footer sections (links, social media)'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'magenta'
    }
  ));
}

function showHookHelp() {
  console.log(boxen(
    chalk.bold.yellow('Hook Components Help') + '\n\n' +
    chalk.white('Hook components are reusable React hooks\n\n') +
    chalk.yellow('EXAMPLES:\n') +
    chalk.white('  npx mvpblocks@latest add use-local-storage\n') +
    chalk.white('  npx mvpblocks@latest add use-api\n') +
    chalk.white('  npx mvpblocks@latest list --hook\n\n') +
    chalk.yellow('COMMON HOOK COMPONENTS:\n') +
    chalk.white('  • State hooks (localStorage, sessionStorage)\n') +
    chalk.white('  • API hooks (fetch, mutations, queries)\n') +
    chalk.white('  • UI hooks (modal, tooltip, dropdown)\n') +
    chalk.white('  • Utility hooks (debounce, throttle, timer)\n') +
    chalk.white('  • Form hooks (validation, submission)'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'yellow'
    }
  ));
}
