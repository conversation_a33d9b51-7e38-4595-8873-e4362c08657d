import ora from 'ora';
import chalk from 'chalk';
import boxen from 'boxen';

export const logger = {
  info: (message) => console.log(chalk.blue(message)),
  success: (message) => console.log(chalk.green(message)),
  warn: (message) => console.log(chalk.yellow(message)),
  error: (message) => console.log(chalk.red(message)),
  spinner: (message) => ora(message).start(),
  box: (message, options = {}) => {
    const defaults = {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'blue',
      backgroundColor: '#222222'
    };
    console.log(boxen(message, { ...defaults, ...options }));
  },
  divider: (char = '─', length = process.stdout.columns - 4) => {
    console.log(chalk.dim(char.repeat(length)));
  }
};