#!/usr/bin/env node
import { program } from 'commander';
import updateNotifier from 'update-notifier';
import packageJson from '../package.json' assert { type: 'json' };
import { checkSystemRequirements } from '../lib/utils/validator.js';
import chalk from 'chalk';
import boxen from 'boxen';

// Check for updates
updateNotifier({ pkg: packageJson }).notify();

// Check system requirements
checkSystemRequirements();

program
  .name('mvpblocks')
  .description('The official MVPBlocks component management CLI')
  .version(packageJson.version, '-v, --version', 'Display version')
  .usage('<command> [options]');

// Help command
program
  .command('help')
  .description('Display help information')
  .option('--ui', 'Show UI component specific help')
  .option('--lib', 'Show library component specific help')
  .option('--block', 'Show block component specific help')
  .option('--hook', 'Show hook component specific help')
  .action(async (options) => {
    const { helpAction } = await import('../lib/actions/help.js');
    return helpAction(options);
  });

// Add command - works without additional options
program
  .command('add <component>')
  .description('Add a component to your project (e.g., npx mvpblocks@latest add about-us-1)')
  .option('-f, --force', 'Force reinstall even if exists')
  .option('-d, --dry-run', 'Show what would be installed')
  .option('--no-deps', 'Skip installing dependencies')
  .option('-t, --type <type>', 'Filter by component type (ui, block, hook, lib)')
  .action(async (component, options) => {
    const { addAction } = await import('../lib/actions/add.js');
    return addAction(component, options);
  });

// List command with enhanced flag support
program
  .command('list')
  .description('List available components')
  .option('-a, --all', 'Show all components including dependencies')
  .option('-t, --type <type>', 'Filter by component type (ui, block, hook, lib)')
  .option('--ui', 'Show only UI components')
  .option('--lib', 'Show only library components')
  .option('--block', 'Show only block components')
  .option('--hook', 'Show only hook components')
  .option('-c, --categories', 'List by categories')
  .option('-j, --json', 'Output as JSON')
  .option('--detailed', 'Show detailed component information')
  .action(async (options) => {
    const { listAction } = await import('../lib/actions/list.js');
    return listAction(options);
  });

// Default help when no command is provided
program.action(() => {
  console.log('\n' + boxen(
    chalk.bold.blue('🚀 MVPBlocks CLI') + '\n\n' +
    chalk.white('The ultimate component management tool for modern web development\n') +
    chalk.dim('Built on top of shadcn/ui • Powered by MVPBlocks\n\n') +
    chalk.yellow('✨ Quick Start:\n') +
    chalk.white('  npx mvpblocks@latest add about-us-1\n') +
    chalk.white('  npx mvpblocks@latest list --ui\n') +
    chalk.white('  npx mvpblocks@latest help --block\n\n') +
    chalk.yellow('📚 Available Commands:\n') +
    chalk.white('  help     Display detailed help information\n') +
    chalk.white('  add      Add a component to your project\n') +
    chalk.white('  list     Browse available components\n\n') +
    chalk.cyan('🌐 Registry: ') + chalk.dim('https://blocks.mvp-subha.me\n') +
    chalk.cyan('📖 Docs: ') + chalk.dim('https://mvpblocks.com'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'blue',
      backgroundColor: '#001122'
    }
  ));

  console.log(chalk.dim('\n💡 Tip: Use "npx mvpblocks@latest help" for detailed usage information\n'));
});

program.showHelpAfterError();
program.parse(process.argv);