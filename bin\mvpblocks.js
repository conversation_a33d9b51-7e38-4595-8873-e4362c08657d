#!/usr/bin/env node
import { program } from 'commander';
import updateNotifier from 'update-notifier';
import packageJson from '../package.json' assert { type: 'json' };
import { checkSystemRequirements } from '../lib/utils/validator.js';

// Check for updates
updateNotifier({ pkg: packageJson }).notify();

// Check system requirements
checkSystemRequirements();

program
  .name('mvpblocks')
  .description('The official MVPBlocks component management CLI')
  .version(packageJson.version, '-v, --version', 'Display version')
  .usage('<command> [options]');

// Lazy-loaded commands
program
  .command('add <component>')
  .description('Add a component to your project')
  .option('-f, --force', 'Force reinstall even if exists')
  .option('-d, --dry-run', 'Show what would be installed')
  .option('--no-deps', 'Skip installing dependencies')
  .option('-t, --type <type>', 'Filter by component type (ui, block, hook, lib)')
  .action(async (component, options) => {
    const { addAction } = await import('../lib/actions/add.js');
    return addAction(component, options);
  });

program
  .command('list')
  .description('List available components')
  .option('-a, --all', 'Show all components including dependencies')
  .option('-t, --type <type>', 'Filter by component type (ui, block, hook, lib)')
  .option('-c, --categories', 'List by categories')
  .option('-j, --json', 'Output as JSON')
  .action(async (options) => {
    const { listAction } = await import('../lib/actions/list.js');
    return listAction(options);
  });

// Add other commands with the same lazy-loading pattern...

program.showHelpAfterError();
program.parse(process.argv);