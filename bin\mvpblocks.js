#!/usr/bin/env node
import { program } from 'commander';
import updateNotifier from 'update-notifier';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import chalk from 'chalk';
import boxen from 'boxen';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const packageJson = JSON.parse(readFileSync(join(__dirname, '../package.json'), 'utf8'));

// Check for updates
updateNotifier({ pkg: packageJson }).notify();

program
  .name('mvpblocks')
  .description('The official MVPBlocks component management CLI')
  .version(packageJson.version, '-v, --version', 'Display version')
  .usage('<command> [options]');

// Help command
program
  .command('help')
  .description('Display help information')
  .option('--ui', 'Show UI component specific help')
  .option('--lib', 'Show library component specific help')
  .option('--block', 'Show block component specific help')
  .option('--hook', 'Show hook component specific help')
  .action(async (options) => {
    const { showHelp } = await import('../lib/help.js');
    return showHelp(options);
  });

// Add command - works without additional options
program
  .command('add <component>')
  .description('Add a component to your project (e.g., npx mvpblocks@latest add about-us-1)')
  .action(async (component) => {
    const { addComponent } = await import('../lib/add.js');
    return addComponent(component);
  });

// List command with enhanced flag support
program
  .command('list')
  .description('List available components')
  .option('--ui', 'Show only UI components')
  .option('--lib', 'Show only library components')
  .option('--block', 'Show only block components')
  .option('--hook', 'Show only hook components')
  .action(async (options) => {
    const { listComponents } = await import('../lib/list.js');
    return listComponents(options);
  });

// Default help when no command is provided
program.action(() => {
  console.log('\n' + boxen(
    chalk.bold.blue('🚀 MVPBlocks CLI') + '\n\n' +
    chalk.white('The ultimate component management tool for modern web development\n') +
    chalk.dim('Built on top of shadcn/ui • Powered by MVPBlocks\n\n') +
    chalk.yellow('✨ Quick Start:\n') +
    chalk.white('  npx mvpblocks@latest add about-us-1\n') +
    chalk.white('  npx mvpblocks@latest list --ui\n') +
    chalk.white('  npx mvpblocks@latest help --block\n\n') +
    chalk.yellow('📚 Available Commands:\n') +
    chalk.white('  help     Display detailed help information\n') +
    chalk.white('  add      Add a component to your project\n') +
    chalk.white('  list     Browse available components\n\n') +
    chalk.cyan('🌐 Registry: ') + chalk.dim('https://blocks.mvp-subha.me\n') +
    chalk.cyan('📖 Docs: ') + chalk.dim('https://mvpblocks.com'),
    {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'blue',
      backgroundColor: '#001122'
    }
  ));

  console.log(chalk.dim('\n💡 Tip: Use "npx mvpblocks@latest help" for detailed usage information\n'));
});

program.showHelpAfterError();
program.parse(process.argv);