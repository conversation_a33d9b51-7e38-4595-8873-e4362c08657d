import { z } from 'zod';

export const registryItemTypeSchema = z.enum([
  'registry:block',
  'registry:ui',
  'registry:hook',
  'registry:lib',
]);

export const registryItemFileSchema = z.object({
  path: z.string(),
  content: z.string().optional(),
  type: registryItemTypeSchema,
  target: z.string().optional(),
});

export const registryItemSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  type: registryItemTypeSchema,
  dependencies: z.array(z.string()).optional(),
  registryDependencies: z.array(z.string()).optional(),
  files: z.array(z.object({
    path: z.string(),
    content: z.string().optional(),
    type: registryItemTypeSchema,
    target: z.string().optional()
  })).optional(),
  categories: z.array(z.string()).optional(),
  docs: z.string().optional(),
  meta: z.record(z.string(), z.any()).optional()
});

export const configSchema = z.object({
  baseUrl: z.string().url().default('https://blocks.mvp-subha.me/r'),
  componentsDir: z.string().default('components/mvpblocks'),
  typescript: z.boolean().default(true),
  preferOnlineDocs: z.boolean().default(true),
  cacheTTL: z.number().default(86400)
});