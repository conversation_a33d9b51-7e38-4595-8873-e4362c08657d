{"name": "mvpblocks", "version": "1.2.0", "description": "MVPBlocks CLI - The ultimate component management tool for fetching and managing UI components", "main": "./bin/mvpblocks.js", "bin": {"mvpblocks": "./bin/mvpblocks.js"}, "type": "module", "engines": {"node": ">=18.0.0"}, "files": ["bin/", "lib/", "package.json", "README.md"], "dependencies": {"axios": "^1.6.7", "boxen": "^7.1.1", "chalk": "^5.3.0", "chalk-animation": "^2.0.3", "commander": "^11.1.0", "conf": "^11.0.1", "execa": "^8.0.1", "fs-extra": "^11.2.0", "inquirer": "^9.2.10", "listr2": "^6.6.1", "ora": "^7.0.1", "table": "^6.9.0", "update-notifier": "^7.0.0", "zod": "^3.22.4"}, "keywords": ["mvpblocks", "cli", "components", "ui", "blocks", "npx", "component-library", "frontend"], "repository": {"type": "git", "url": "https://github.com/mvpblocks/mvpblocks-cli.git"}, "homepage": "https://mvpblocks.com", "bugs": {"url": "https://github.com/mvpblocks/mvpblocks-cli/issues"}, "license": "MIT", "author": "MVPBlocks Team"}