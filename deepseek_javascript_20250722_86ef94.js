// lib/actions.js
import axios from 'axios';
import { execa } from 'execa';
import ora from 'ora';
import chalk from 'chalk';
import { promises as fs } from 'fs';
import path from 'path';

const BASE_URL = 'https://blocks.mvp-subha.me/r';

export async function addComponent(componentName) {
  const spinner = ora(`Adding ${componentName}...`).start();
  
  try {
    // 1. Fetch the component JSON
    const response = await axios.get(`${BASE_URL}/${componentName}.json`);
    const componentData = response.data;

    // 2. Process dependencies first
    if (componentData.registryDependencies && componentData.registryDependencies.length > 0) {
      spinner.text = `Processing dependencies for ${componentName}...`;
      for (const dep of componentData.registryDependencies) {
        const depName = dep.split('/').pop().replace('.json', '');
        await addComponent(depName);
      }
    }

    // 3. Save the processed component file
    const componentDir = path.join(process.cwd(), 'components/mvpblocks');
    await fs.mkdir(componentDir, { recursive: true });
    
    componentData.files.forEach(async (file) => {
      const filePath = path.join(process.cwd(), file.target);
      await fs.mkdir(path.dirname(filePath), { recursive: true });
      await fs.writeFile(filePath, file.content);
    });

    spinner.succeed(`Added ${chalk.green(componentName)} successfully!`);
  } catch (error) {
    spinner.fail(`Failed to add ${chalk.red(componentName)}`);
    console.error(error.message);
  }
}