import { execa } from 'execa';
import { getConfig } from './config.js';
import { logger } from './logger.js';

export async function fetchComponents() {
  const config = await getConfig();
  const { data } = await axios.get(`${config.baseUrl}/components.json`);
  return data;
}

export async function installDependencies(dependencies) {
  if (!dependencies?.length) return;

  const spinner = logger.spinner(`Installing ${dependencies.length} dependencies...`);
  
  try {
    await execa('npm', ['install', ...dependencies, '--save'], {
      stdio: 'inherit'
    });
    spinner.succeed();
  } catch (error) {
    spinner.fail();
    throw error;
  }
}

export async function uninstallDependencies(dependencies) {
  if (!dependencies?.length) return;

  const spinner = logger.spinner(`Removing ${dependencies.length} dependencies...`);
  
  try {
    await execa('npm', ['uninstall', ...dependencies], {
      stdio: 'inherit'
    });
    spinner.succeed();
  } catch (error) {
    spinner.fail();
    throw error;
  }
}