import axios from 'axios';
import { execa } from 'execa';
import { getConfig } from './config.js';
import { logger } from './logger.js';

export async function fetchComponents() {
  const config = await getConfig();

  try {
    const { data } = await axios.get(`${config.baseUrl}/components.json`, {
      timeout: 10000,
      headers: {
        'User-Agent': 'mvpblocks-cli/1.2.0'
      }
    });
    return data;
  } catch (error) {
    // If the components.json endpoint doesn't exist, return mock data for testing
    if (error.response?.status === 404 || error.code === 'ENOTFOUND') {
      logger.warn('Components registry not available, using mock data for demonstration');
      return getMockComponents();
    }
    throw error;
  }
}

function getMockComponents() {
  return [
    {
      name: 'about-us-1',
      description: 'A modern about us section with team photos and company info',
      type: 'registry:block',
      categories: ['landing', 'about'],
      dependencies: ['react', 'tailwindcss'],
      registryDependencies: [],
      files: [
        {
          path: 'components/about-us-1.tsx',
          content: '// About Us Component\nexport default function AboutUs() {\n  return <div>About Us Section</div>;\n}',
          type: 'registry:block',
          target: 'components/mvpblocks/about-us-1.tsx'
        }
      ]
    },
    {
      name: 'button-primary',
      description: 'A primary button component with hover effects',
      type: 'registry:ui',
      categories: ['buttons', 'ui'],
      dependencies: ['react'],
      registryDependencies: [],
      files: [
        {
          path: 'components/button-primary.tsx',
          content: '// Primary Button Component\nexport default function ButtonPrimary() {\n  return <button>Primary Button</button>;\n}',
          type: 'registry:ui',
          target: 'components/mvpblocks/button-primary.tsx'
        }
      ]
    },
    {
      name: 'use-local-storage',
      description: 'A React hook for managing localStorage state',
      type: 'registry:hook',
      categories: ['hooks', 'storage'],
      dependencies: ['react'],
      registryDependencies: [],
      files: [
        {
          path: 'hooks/use-local-storage.ts',
          content: '// Local Storage Hook\nexport function useLocalStorage(key: string, defaultValue: any) {\n  // Hook implementation\n}',
          type: 'registry:hook',
          target: 'hooks/use-local-storage.ts'
        }
      ]
    },
    {
      name: 'api-client',
      description: 'A utility library for making API requests',
      type: 'registry:lib',
      categories: ['utilities', 'api'],
      dependencies: ['axios'],
      registryDependencies: [],
      files: [
        {
          path: 'lib/api-client.ts',
          content: '// API Client Library\nexport class ApiClient {\n  // Implementation\n}',
          type: 'registry:lib',
          target: 'lib/api-client.ts'
        }
      ]
    }
  ];
}

export async function installDependencies(dependencies) {
  if (!dependencies?.length) return;

  const spinner = logger.spinner(`Installing ${dependencies.length} dependencies...`);
  
  try {
    await execa('npm', ['install', ...dependencies, '--save'], {
      stdio: 'inherit'
    });
    spinner.succeed();
  } catch (error) {
    spinner.fail();
    throw error;
  }
}

export async function uninstallDependencies(dependencies) {
  if (!dependencies?.length) return;

  const spinner = logger.spinner(`Removing ${dependencies.length} dependencies...`);
  
  try {
    await execa('npm', ['uninstall', ...dependencies], {
      stdio: 'inherit'
    });
    spinner.succeed();
  } catch (error) {
    spinner.fail();
    throw error;
  }
}