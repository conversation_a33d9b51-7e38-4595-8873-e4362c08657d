import axios from 'axios';
import { execa } from 'execa';
import { getConfig } from './config.js';
import { logger } from './logger.js';

export async function fetchComponents() {
  // For listing components, we'll need to implement a registry index
  // For now, return a basic list of known components
  // In a real implementation, this would fetch from a registry index
  logger.info('Fetching available components from MVPBlocks registry...');

  // This would ideally fetch from an index endpoint
  // For now, we'll return some known components
  return [
    {
      name: 'about-us-1',
      description: 'A modern about us section with team photos and company info',
      type: 'registry:block',
      categories: ['landing', 'about'],
      dependencies: ['framer-motion', 'lucide-react', 'react']
    },
    {
      name: 'border-beam',
      description: 'An animated border beam effect component',
      type: 'registry:ui',
      categories: ['effects', 'ui'],
      dependencies: ['react']
    },
    {
      name: 'pulse-card',
      description: 'A card component with pulse hover effects',
      type: 'registry:ui',
      categories: ['cards', 'ui'],
      dependencies: ['react']
    },
    {
      name: 'spotlight',
      description: 'A spotlight effect component for highlighting content',
      type: 'registry:ui',
      categories: ['effects', 'ui'],
      dependencies: ['react']
    }
  ];
}

export async function installDependencies(dependencies) {
  if (!dependencies?.length) return;

  const spinner = logger.spinner(`Installing ${dependencies.length} dependencies...`);
  
  try {
    await execa('npm', ['install', ...dependencies, '--save'], {
      stdio: 'inherit'
    });
    spinner.succeed();
  } catch (error) {
    spinner.fail();
    throw error;
  }
}

export async function uninstallDependencies(dependencies) {
  if (!dependencies?.length) return;

  const spinner = logger.spinner(`Removing ${dependencies.length} dependencies...`);
  
  try {
    await execa('npm', ['uninstall', ...dependencies], {
      stdio: 'inherit'
    });
    spinner.succeed();
  } catch (error) {
    spinner.fail();
    throw error;
  }
}