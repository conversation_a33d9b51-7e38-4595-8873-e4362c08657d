# MVPBlocks CLI

🚀 The ultimate component management tool for modern web development, built on top of shadcn/ui.

**No installation required!** Use `npx mvpblocks@latest` to access 200+ premium components instantly.

## Quick Start

```bash
# Add a component to your project
npx mvpblocks@latest add about-us-1

# List available UI components
npx mvpblocks@latest list --ui

# Get help for block components
npx mvpblocks@latest help --block
```

## Installation

No installation required! Use npx to run the CLI directly:

```bash
npx mvpblocks@latest <command>
```

## Commands

### `add <component>`

Add a component to your project. The component will be fetched from the MVPBlocks registry and installed with all its dependencies.

```bash
# Basic usage
npx mvpblocks@latest add about-us-1

# Preview what would be installed (dry run)
npx mvpblocks@latest add about-us-1 --dry-run

# Force reinstall even if component exists
npx mvpblocks@latest add about-us-1 --force

# Skip installing npm dependencies
npx mvpblocks@latest add about-us-1 --no-deps
```

**Options:**
- `--dry-run, -d` - Show what would be installed without making changes
- `--force, -f` - Force reinstall even if component already exists
- `--no-deps` - Skip installing npm dependencies

### `list`

Browse and filter available components from the MVPBlocks registry.

```bash
# List all components
npx mvpblocks@latest list

# Filter by component type
npx mvpblocks@latest list --ui
npx mvpblocks@latest list --block
npx mvpblocks@latest list --hook
npx mvpblocks@latest list --lib

# Show detailed information
npx mvpblocks@latest list --detailed

# Group by categories
npx mvpblocks@latest list --categories

# Output as JSON
npx mvpblocks@latest list --json
```

**Options:**
- `--ui` - Show only UI components
- `--block` - Show only block components  
- `--hook` - Show only hook components
- `--lib` - Show only library components
- `--detailed` - Show detailed component information
- `--categories, -c` - Group components by categories
- `--json, -j` - Output results as JSON
- `--all, -a` - Show all components including dependencies

### `help`

Get detailed help information for using the CLI and understanding component types.

```bash
# General help
npx mvpblocks@latest help

# Component-specific help
npx mvpblocks@latest help --ui
npx mvpblocks@latest help --block
npx mvpblocks@latest help --hook
npx mvpblocks@latest help --lib
```

## Component Types

### 🎨 UI Components (`--ui`)
Reusable interface elements like buttons, forms, navigation, and layout components.

**Examples:** `button-primary`, `modal-dialog`, `navbar`, `card`

### 🧱 Block Components (`--block`)
Complete page sections ready to use in your application.

**Examples:** `about-us-1`, `hero-section`, `contact-form`, `pricing-table`

### 🪝 Hook Components (`--hook`)
Custom React hooks for state management and reusable logic.

**Examples:** `use-local-storage`, `use-api`, `use-debounce`

### 📚 Library Components (`--lib`)
Utility functions, API clients, and helper libraries.

**Examples:** `api-client`, `validation-utils`, `date-helpers`

## Registry

Components are fetched from the MVPBlocks registry at `https://blocks.mvp-subha.me/r/`

Each component includes:
- ✅ TypeScript support
- 📦 Automatic dependency management
- 🎨 Tailwind CSS styling
- 📱 Responsive design
- ♿ Accessibility features

## Examples

```bash
# Add a modern about us section
npx mvpblocks@latest add about-us-1

# Add UI components
npx mvpblocks@latest add button-primary
npx mvpblocks@latest add modal-dialog

# Add utility hooks
npx mvpblocks@latest add use-local-storage

# Browse available components
npx mvpblocks@latest list --ui --detailed
```

## File Structure

Components are installed to:
- **UI Components:** `components/ui/`
- **Block Components:** `components/blocks/`
- **Hooks:** `hooks/`
- **Libraries:** `lib/`

## Requirements

- Node.js 18.0.0 or higher
- npm 7.0.0 or higher
- A React/Next.js project with Tailwind CSS

## Links

- 🌐 **Registry:** https://blocks.mvp-subha.me
- 📖 **Documentation:** https://mvpblocks.com
- 🐛 **Issues:** https://github.com/mvpblocks/mvpblocks-cli/issues

---

Built with ❤️ by the MVPBlocks team
