import axios from 'axios';
import ora from 'ora';
import chalk from 'chalk';
import { table } from 'table';
import { registryItemTypeSchema } from '../schemas.js';
import { getConfig } from '../utils/config.js';
import { fetchComponents } from '../utils/installer.js';

export async function listAction(options) {
  const spinner = ora('Fetching components...').start();
  const config = await getConfig();

  try {
    const components = await fetchComponents();
    
    // Filter by type if specified
    let filteredComponents = components;
    if (options.type) {
      const typeValidation = registryItemTypeSchema.safeParse(`registry:${options.type}`);
      if (!typeValidation.success) {
        spinner.fail(`Invalid type: ${options.type}. Valid types are ui, block, hook, lib`);
        return;
      }
      
      filteredComponents = components.filter(
        c => c.type === `registry:${options.type}`
      );
    }

    spinner.stop();

    if (options.json) {
      console.log(JSON.stringify(filteredComponents, null, 2));
      return;
    }

    if (options.categories) {
      displayByCategories(filteredComponents);
    } else {
      displayAsTable(filteredComponents, options.all);
    }
  } catch (error) {
    spinner.fail('Failed to fetch components');
    console.error(chalk.red(error.message));
    process.exit(1);
  }
}

function displayAsTable(components, showAll) {
  const data = [
    [
      chalk.bold('Name'),
      chalk.bold('Type'),
      chalk.bold('Description'),
      chalk.bold('Dependencies')
    ]
  ];

  components.forEach(comp => {
    data.push([
      chalk.blue(comp.name),
      formatType(comp.type),
      comp.description || 'No description',
      comp.dependencies?.length ? comp.dependencies.join(', ') : 'None'
    ]);
  });

  console.log(table(data));
  console.log(chalk.dim(`\nShowing ${components.length} components`));
  if (!showAll) {
    console.log(chalk.dim('Use --all to show dependencies'));
  }
}

function displayByCategories(components) {
  const categories = {};
  
  components.forEach(comp => {
    comp.categories?.forEach(cat => {
      if (!categories[cat]) categories[cat] = [];
      categories[cat].push(comp);
    });
    
    if (!comp.categories?.length) {
      if (!categories.uncategorized) categories.uncategorized = [];
      categories.uncategorized.push(comp);
    }
  });

  Object.entries(categories).forEach(([category, items]) => {
    console.log(chalk.bold(`\n${category.toUpperCase()}`));
    items.forEach(item => {
      console.log(`  • ${chalk.blue(item.name)} - ${item.description || 'No description'}`);
    });
  });
}

function formatType(type) {
  const typeMap = {
    'registry:ui': chalk.green('UI'),
    'registry:block': chalk.magenta('Block'),
    'registry:hook': chalk.yellow('Hook'),
    'registry:lib': chalk.cyan('Lib')
  };
  return typeMap[type] || type;
}