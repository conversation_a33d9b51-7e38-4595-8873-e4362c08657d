import ora from 'ora';
import chalk from 'chalk';
import { table } from 'table';
import { registryItemTypeSchema } from '../schemas.js';
import { fetchComponents } from '../utils/installer.js';

export async function listAction(options = {}) {
  const spinner = ora('Fetching components...').start();

  try {
    const components = await fetchComponents();

    // Filter by type if specified (multiple ways to specify type)
    let filteredComponents = components;
    let filterType = null;

    // Check for specific type flags
    if (options.ui) {
      filterType = 'ui';
    } else if (options.lib) {
      filterType = 'lib';
    } else if (options.block) {
      filterType = 'block';
    } else if (options.hook) {
      filterType = 'hook';
    } else if (options.type) {
      filterType = options.type;
    }

    if (filterType) {
      const typeValidation = registryItemTypeSchema.safeParse(`registry:${filterType}`);
      if (!typeValidation.success) {
        spinner.fail(`Invalid type: ${filterType}. Valid types are ui, block, hook, lib`);
        return;
      }

      filteredComponents = components.filter(
        c => c.type === `registry:${filterType}`
      );
    }

    spinner.stop();

    if (filteredComponents.length === 0) {
      console.log(chalk.yellow('No components found matching the specified criteria.'));
      return;
    }

    if (options.json) {
      console.log(JSON.stringify(filteredComponents, null, 2));
      return;
    }

    // Show filter information
    if (filterType) {
      console.log(chalk.blue(`\nShowing ${filterType.toUpperCase()} components:\n`));
    }

    if (options.categories) {
      displayByCategories(filteredComponents, options.detailed);
    } else {
      displayAsTable(filteredComponents, options.all, options.detailed);
    }
  } catch (error) {
    spinner.fail('Failed to fetch components');
    console.error(chalk.red(error.message));
    process.exit(1);
  }
}

function displayAsTable(components, showAll, detailed) {
  const headers = [
    chalk.bold('Name'),
    chalk.bold('Type'),
    chalk.bold('Description')
  ];

  if (detailed) {
    headers.push(chalk.bold('Categories'), chalk.bold('Dependencies'), chalk.bold('Registry Deps'));
  } else {
    headers.push(chalk.bold('Dependencies'));
  }

  const data = [headers];

  components.forEach(comp => {
    const row = [
      chalk.blue(comp.name),
      formatType(comp.type),
      truncateText(comp.description || 'No description', detailed ? 50 : 30)
    ];

    if (detailed) {
      row.push(
        comp.categories?.length ? comp.categories.join(', ') : 'None',
        comp.dependencies?.length ? comp.dependencies.join(', ') : 'None',
        comp.registryDependencies?.length ? comp.registryDependencies.length.toString() : '0'
      );
    } else {
      row.push(comp.dependencies?.length ? comp.dependencies.join(', ') : 'None');
    }

    data.push(row);
  });

  console.log(table(data, {
    border: {
      topBody: '─',
      topJoin: '┬',
      topLeft: '┌',
      topRight: '┐',
      bottomBody: '─',
      bottomJoin: '┴',
      bottomLeft: '└',
      bottomRight: '┘',
      bodyLeft: '│',
      bodyRight: '│',
      bodyJoin: '│',
      joinBody: '─',
      joinLeft: '├',
      joinRight: '┤',
      joinJoin: '┼'
    }
  }));

  console.log(chalk.dim(`\nShowing ${components.length} components`));
  if (!showAll && !detailed) {
    console.log(chalk.dim('Use --all to show dependencies or --detailed for more information'));
  }
}

function displayByCategories(components, detailed) {
  const categories = {};

  components.forEach(comp => {
    comp.categories?.forEach(cat => {
      if (!categories[cat]) categories[cat] = [];
      categories[cat].push(comp);
    });

    if (!comp.categories?.length) {
      if (!categories.uncategorized) categories.uncategorized = [];
      categories.uncategorized.push(comp);
    }
  });

  Object.entries(categories).forEach(([category, items]) => {
    console.log(chalk.bold(`\n${category.toUpperCase()} (${items.length} components)`));
    items.forEach(item => {
      if (detailed) {
        console.log(`  • ${chalk.blue(item.name)} [${formatType(item.type)}]`);
        console.log(`    ${item.description || 'No description'}`);
        if (item.dependencies?.length) {
          console.log(`    Dependencies: ${chalk.dim(item.dependencies.join(', '))}`);
        }
      } else {
        console.log(`  • ${chalk.blue(item.name)} - ${item.description || 'No description'}`);
      }
    });
  });
}

function formatType(type) {
  const typeMap = {
    'registry:ui': chalk.green('UI'),
    'registry:block': chalk.magenta('Block'),
    'registry:hook': chalk.yellow('Hook'),
    'registry:lib': chalk.cyan('Lib')
  };
  return typeMap[type] || type;
}

function truncateText(text, maxLength) {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}