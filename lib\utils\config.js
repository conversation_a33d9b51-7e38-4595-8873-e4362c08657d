import Conf from 'conf';
import { configSchema } from '../schemas.js';

const config = new Conf({
  projectName: 'mvpblocks',
  schema: configSchema.shape
});

export async function getConfig() {
  return configSchema.parse(config.store);
}

export async function setConfig(newConfig) {
  const validated = configSchema.parse(newConfig);
  config.set(validated);
  return validated;
}

export async function resetConfig() {
  config.clear();
  return getConfig();
}