import { DEFAULT_CONFIG } from '../constants.js';

export async function getConfig() {
  // For now, just return the default config
  // In a real implementation, this would merge with user config
  return DEFAULT_CONFIG;
}

export async function setConfig(newConfig) {
  // For now, just validate and return the config
  // In a real implementation, this would save to user config
  return { ...DEFAULT_CONFIG, ...newConfig };
}

export async function resetConfig() {
  return DEFAULT_CONFIG;
}