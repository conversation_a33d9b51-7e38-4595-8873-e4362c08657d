import Conf from 'conf';
import { logger } from './logger.js';

const config = new Conf({
  projectName: 'mvpblocks-cache',
  fileExtension: 'json'
});

export async function getCache(key) {
  if (key === '*') {
    return config.store;
  }
  
  const cached = config.get(key);
  if (!cached) return null;
  
  if (cached.expiresAt && cached.expiresAt < Date.now()) {
    config.delete(key);
    return null;
  }
  
  return cached.data;
}

export async function setCache(key, data, ttl = 86400) {
  config.set(key, {
    data,
    expiresAt: Date.now() + (ttl * 1000),
    version: '1.0.0'
  });
}

export async function cleanCache() {
  config.clear();
  logger.success('Cache cleared successfully');
}

export async function cacheStats() {
  const keys = Object.keys(config.store);
  const size = Buffer.from(JSON.stringify(config.store)).length;
  
  return {
    count: keys.length,
    size: `${(size / 1024).toFixed(2)} KB`,
    path: config.path
  };
}