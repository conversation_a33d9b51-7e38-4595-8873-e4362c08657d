import { logger } from './logger.js';

// Simple in-memory cache for demonstration
const cache = new Map();

export async function getCache(key) {
  if (key === '*') {
    return Object.fromEntries(cache);
  }

  const cached = cache.get(key);
  if (!cached) return null;

  if (cached.expiresAt && cached.expiresAt < Date.now()) {
    cache.delete(key);
    return null;
  }

  return cached.data;
}

export async function setCache(key, data, ttl = 86400) {
  cache.set(key, {
    data,
    expiresAt: Date.now() + (ttl * 1000),
    version: '1.0.0'
  });
}

export async function cleanCache() {
  cache.clear();
  logger.success('Cache cleared successfully');
}

export async function cacheStats() {
  const keys = Array.from(cache.keys());
  const size = Buffer.from(JSON.stringify(Object.fromEntries(cache))).length;

  return {
    count: keys.length,
    size: `${(size / 1024).toFixed(2)} KB`,
    path: 'in-memory'
  };
}