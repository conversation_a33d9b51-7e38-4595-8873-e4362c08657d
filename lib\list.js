import axios from 'axios';
import ora from 'ora';
import chalk from 'chalk';
import { table } from 'table';

const REGISTRY_BASE = 'https://blocks.mvp-subha.me/r';

// Known components - in a real implementation, this could be fetched from an index
const KNOWN_COMPONENTS = [
  { name: 'about-us-1', type: 'block', description: 'Modern about us section with team photos' },
  { name: 'about-us-2', type: 'block', description: 'Minimal about us section with stats' },
  { name: 'about-us-3', type: 'block', description: 'Creative about us with timeline' },
  { name: 'hero-1', type: 'block', description: 'Modern hero section with gradient' },
  { name: 'hero-2', type: 'block', description: 'Minimal hero with video background' },
  { name: 'pricing-1', type: 'block', description: 'Clean pricing table with features' },
  { name: 'contact-1', type: 'block', description: 'Contact form with map integration' },
  { name: 'testimonials-1', type: 'block', description: 'Customer testimonials carousel' },
  { name: 'features-1', type: 'block', description: 'Feature showcase with icons' },
  { name: 'cta-1', type: 'block', description: 'Call-to-action section' },
  { name: 'border-beam', type: 'ui', description: 'Animated border beam effect' },
  { name: 'pulse-card', type: 'ui', description: 'Card with pulse hover effect' },
  { name: 'spotlight', type: 'ui', description: 'Spotlight effect component' },
  { name: 'gradient-text', type: 'ui', description: 'Animated gradient text' },
  { name: 'floating-navbar', type: 'ui', description: 'Floating navigation bar' },
  { name: 'use-local-storage', type: 'hook', description: 'Local storage React hook' },
  { name: 'use-debounce', type: 'hook', description: 'Debounce hook for inputs' },
  { name: 'use-intersection', type: 'hook', description: 'Intersection observer hook' },
  { name: 'api-client', type: 'lib', description: 'HTTP client utility' },
  { name: 'form-validator', type: 'lib', description: 'Form validation utilities' }
];

export async function listComponents(options = {}) {
  const spinner = ora('Fetching available components...').start();
  
  try {
    // Filter components based on options
    let components = KNOWN_COMPONENTS;
    
    if (options.ui) {
      components = components.filter(c => c.type === 'ui');
    } else if (options.block) {
      components = components.filter(c => c.type === 'block');
    } else if (options.hook) {
      components = components.filter(c => c.type === 'hook');
    } else if (options.lib) {
      components = components.filter(c => c.type === 'lib');
    }
    
    spinner.stop();
    
    if (components.length === 0) {
      console.log(chalk.yellow('No components found matching the specified criteria.'));
      return;
    }
    
    // Show filter info
    const filterType = options.ui ? 'UI' : options.block ? 'Block' : options.hook ? 'Hook' : options.lib ? 'Library' : 'All';
    console.log(chalk.blue(`\n📦 ${filterType} Components (${components.length} available):\n`));
    
    // Create table
    const tableData = [
      [chalk.bold('Name'), chalk.bold('Type'), chalk.bold('Description'), chalk.bold('Command')]
    ];
    
    components.forEach(comp => {
      const typeColor = {
        'ui': chalk.green('UI'),
        'block': chalk.magenta('Block'),
        'hook': chalk.yellow('Hook'),
        'lib': chalk.cyan('Lib')
      };
      
      tableData.push([
        chalk.blue(comp.name),
        typeColor[comp.type] || comp.type,
        comp.description,
        chalk.dim(`npx mvpblocks@latest add ${comp.name}`)
      ]);
    });
    
    console.log(table(tableData, {
      border: {
        topBody: '─',
        topJoin: '┬',
        topLeft: '┌',
        topRight: '┐',
        bottomBody: '─',
        bottomJoin: '┴',
        bottomLeft: '└',
        bottomRight: '┘',
        bodyLeft: '│',
        bodyRight: '│',
        bodyJoin: '│',
        joinBody: '─',
        joinLeft: '├',
        joinRight: '┤',
        joinJoin: '┼'
      }
    }));
    
    console.log(chalk.dim(`\n💡 Usage: npx mvpblocks@latest add <component-name>`));
    console.log(chalk.dim(`🌐 Registry: ${REGISTRY_BASE}`));
    console.log(chalk.dim(`📖 Browse all: https://blocks.mvp-subha.me\n`));
    
  } catch (error) {
    spinner.fail('Failed to fetch components');
    console.error(chalk.red('Error:'), error.message);
    process.exit(1);
  }
}
